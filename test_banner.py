#!/usr/bin/env python3
"""Test the banner display"""

import os
import time
from colorama import init, Fore, Style

# Initialize colorama
init(autoreset=True)

# Color definitions
GREEN_COLOR = Fore.GREEN
WHITE_COLOR = Fore.WHITE
BRIGHT_STYLE = Style.BRIGHT
RESET_COLOR = Style.RESET_ALL

def clear_screen():
    """Clear the terminal screen"""
    try:
        os.system('cls' if os.name == 'nt' else 'clear')
    except:
        print('\n' * 50)

def show_banner():
    """Display the application banner"""
    print("\n")
    print(GREEN_COLOR + BRIGHT_STYLE + "=" * 100)
    print(GREEN_COLOR + BRIGHT_STYLE + "                           ")
    print(WHITE_COLOR + BRIGHT_STYLE + "███╗   ███╗████████╗      ██████╗ ██╗   ██╗ ██████╗ ")
    print(WHITE_COLOR + BRIGHT_STYLE + "████╗ ████║╚══██╔══╝      ██╔══██╗██║   ██║██╔════╝ ")
    print(WHITE_COLOR + BRIGHT_STYLE + "██╔████╔██║   ██║   █████╗██████╔╝██║   ██║██║  ███╗")
    print(WHITE_COLOR + BRIGHT_STYLE + "██║╚██╔╝██║   ██║   ╚════╝██╔══██╗██║   ██║██║   ██║")
    print(WHITE_COLOR + BRIGHT_STYLE + "██║ ╚═╝ ██║   ██║         ██████╔╝╚██████╔╝╚██████╔╝")
    print(WHITE_COLOR + BRIGHT_STYLE + "╚═╝     ╚═╝   ╚═╝         ╚═════╝  ╚═════╝  ╚═════╝ ")
    print(GREEN_COLOR + BRIGHT_STYLE + "                                         ")
    print(WHITE_COLOR + BRIGHT_STYLE + "                    TIMEZONE :- UTC +6:00")
    print(WHITE_COLOR + BRIGHT_STYLE + "                    VERSION :- 0.3 [RUNNING]")
    print(WHITE_COLOR + BRIGHT_STYLE + "                    TELEGRAM :- @TRADERMAHI")
    print(GREEN_COLOR + BRIGHT_STYLE + "=" * 100)
    print(RESET_COLOR)

if __name__ == "__main__":
    clear_screen()
    show_banner()
    print("Banner test complete!")
    time.sleep(3)
