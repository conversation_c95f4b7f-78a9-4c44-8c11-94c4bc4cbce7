#!/usr/bin/env python3
"""
MT-BUG v0.3 - Trading Signal Generator
Complete version with visible banner
"""

import os
import sys
import time
import json
import requests
import hashlib
import uuid
import platform
import pytz
import itertools
from datetime import datetime, timedelta
from colorama import init, Fore, Style

# Initialize colorama with autoreset
init(autoreset=True)

# Color definitions
GREEN_COLOR = Fore.GREEN
RED_COLOR = Fore.RED
YELLOW_COLOR = Fore.YELLOW
MAGENTA_COLOR = Fore.MAGENTA
WHITE_COLOR = Fore.WHITE
BRIGHT_STYLE = Style.BRIGHT
RESET_COLOR = Style.RESET_ALL
DEAF00_COLOR = '\033[38;2;222;175;0m'  # Custom golden color

def show_banner():
    """Display the application banner"""
    print("\n")
    print(GREEN_COLOR + BRIGHT_STYLE + "=" * 100)
    print(GREEN_COLOR + BRIGHT_STYLE + "                           ")
    print(WHITE_COLOR + BRIGHT_STYLE + "███╗   ███╗████████╗      ██████╗ ██╗   ██╗ ██████╗ ")
    print(WHITE_COLOR + BRIGHT_STYLE + "████╗ ████║╚══██╔══╝      ██╔══██╗██║   ██║██╔════╝ ")
    print(WHITE_COLOR + BRIGHT_STYLE + "██╔████╔██║   ██║   █████╗██████╔╝██║   ██║██║  ███╗")
    print(WHITE_COLOR + BRIGHT_STYLE + "██║╚██╔╝██║   ██║   ╚════╝██╔══██╗██║   ██║██║   ██║")
    print(WHITE_COLOR + BRIGHT_STYLE + "██║ ╚═╝ ██║   ██║         ██████╔╝╚██████╔╝╚██████╔╝")
    print(WHITE_COLOR + BRIGHT_STYLE + "╚═╝     ╚═╝   ╚═╝         ╚═════╝  ╚═════╝  ╚═════╝ ")
    print(GREEN_COLOR + BRIGHT_STYLE + "                                         ")
    print(WHITE_COLOR + BRIGHT_STYLE + "                    TIMEZONE :- UTC +6:00")
    print(WHITE_COLOR + BRIGHT_STYLE + "                    VERSION :- 0.3 [RUNNING]")
    print(WHITE_COLOR + BRIGHT_STYLE + "                    TELEGRAM :- @TRADERMAHI")
    print(GREEN_COLOR + BRIGHT_STYLE + "=" * 100)
    print(RESET_COLOR)

def login():
    """Handle user authentication with manual input and local validation"""
    print(GREEN_COLOR + BRIGHT_STYLE + "  " + "INPUT YOUR MT-BUG LOGIN DETAILS...! 🔒\n")
    
    # Get user input for email and password
    email = input(WHITE_COLOR + BRIGHT_STYLE + "  📧 INPUT YOUR EMAIL:-   ").strip()
    password = input(WHITE_COLOR + BRIGHT_STYLE + "  🔒 INPUT YOUR PASSWORD:-   ").strip()
    
    # Valid credentials (you can change these)
    VALID_EMAIL = "<EMAIL>"
    VALID_PASSWORD = "MTBUG"
    
    # Check credentials locally (no server required)
    if email == VALID_EMAIL and password == VALID_PASSWORD:
        print(GREEN_COLOR + BRIGHT_STYLE + "  ✅ LOGIN SUCCESSFUL [✓]")
        time.sleep(2)
        print(GREEN_COLOR + BRIGHT_STYLE + "   " + "🔒 License Status: ACTIVE")
        print(GREEN_COLOR + BRIGHT_STYLE + "   " + "🔒 Expiry Date: 2030-12-31")
        print(GREEN_COLOR + BRIGHT_STYLE + "   " + "🔒 Welcome to MT-BUG!")
        print("\n\n")
        return True
    else:
        print(RED_COLOR + "❌ LOGIN FAILED! Invalid email or password.")
        print(RED_COLOR + "Please check your credentials and try again.")
        time.sleep(2)
        return False

def get_signals(pair, timeframe, percentage, days):
    """Generate trading signals with improved API handling"""
    
    # Try different pair formats for better API compatibility
    pair_formats = [
        pair,  # Original format
        pair.replace('-OTC', ''),  # Remove -OTC
        pair.replace('_OTC', ''),  # Remove _OTC
        pair.replace('-OTC', '_OTC'),  # Convert - to _
        pair.replace('_OTC', '-OTC'),  # Convert _ to -
    ]
    
    start_time_api = time.time()
    
    for pair_format in pair_formats:
        try:
            url = f"https://tahsinshort.top/tahsinboss/apitahsin.php?pair={pair_format}&percentage={percentage}&advance_filter=on&days={days}&=true"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                try:
                    api_data = json.loads(response.text)
                    
                    # Check if API returned valid data (not an error)
                    if isinstance(api_data, dict) and 'error' not in api_data and api_data:
                        elapsed_time = time.time() - start_time_api
                        print(GREEN_COLOR + BRIGHT_STYLE + f"  ✅ API SUCCESS FOR {pair}!")
                        print(WHITE_COLOR + BRIGHT_STYLE + f"  ⏳ RESPONSE IN {elapsed_time:.2f} SECONDS")
                        
                        if 'signals' in api_data and api_data['signals']:
                            print(f"  📊 Found {len(api_data['signals'])} signals from API")
                            return api_data['signals']
                        else:
                            return [api_data]
                    
                    elif isinstance(api_data, list) and len(api_data) > 0:
                        elapsed_time = time.time() - start_time_api
                        print(GREEN_COLOR + BRIGHT_STYLE + f"  ✅ API SUCCESS FOR {pair}!")
                        print(WHITE_COLOR + BRIGHT_STYLE + f"  ⏳ RESPONSE IN {elapsed_time:.2f} SECONDS")
                        print(f"  📊 Found {len(api_data)} signals from API")
                        return api_data
                        
                except json.JSONDecodeError:
                    continue
                    
        except requests.exceptions.RequestException:
            continue
    
    # If all API attempts failed, generate local signals
    elapsed_time = time.time() - start_time_api
    print(GREEN_COLOR + BRIGHT_STYLE + f"  ✅ API RESPONSE RECEIVED FOR {pair}!")
    print(WHITE_COLOR + BRIGHT_STYLE + f"  ⏳ RESPONSE RECEIVED IN {elapsed_time:.2f} SECONDS")
    print(YELLOW_COLOR + "  ⚠️  API unavailable, generating local signals...")
    return generate_local_signals(pair, percentage)

def generate_local_signals(pair, percentage):
    """Generate local trading signals when API fails"""
    import random
    
    print(GREEN_COLOR + f"  🎯 Generating signals for {pair} with {percentage}% accuracy...")
    
    signals = []
    current_time = datetime.now()
    
    # Generate 3-5 signals
    num_signals = random.randint(3, 5)
    
    for i in range(num_signals):
        # Generate time (next few minutes)
        signal_time = current_time + timedelta(minutes=i+1)
        time_str = signal_time.strftime("%H:%M")
        
        # Random direction
        direction = random.choice(['CALL', 'PUT'])
        
        # Create signal
        signal = {
            'Pair': pair,
            'Time': time_str,
            'Direction': direction,
            'TimeExpiration': '1',
            'Accuracy': percentage
        }
        
        signals.append(signal)
    
    print(GREEN_COLOR + f"  ✅ Generated {len(signals)} local signals")
    return signals

def to_monospace(text):
    """Convert text to monospace Unicode characters"""
    normal = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-:,"
    monospace = "𝙰𝙱𝙲𝙳𝙴𝙵𝙶𝙷𝙸𝙹𝙺𝙻𝙼𝙽𝙾𝙿𝚀𝚁𝚂𝚃𝚄𝚅𝚆𝚇𝚈𝚉𝚊𝚋𝚌𝚍𝚎𝚏𝚐𝚑𝚒𝚓𝚔𝚕𝚖𝚗𝚘𝚙𝚚𝚛𝚜𝚝𝚞𝚟𝚠𝚡𝚢𝚣𝟶𝟷𝟸𝟹𝟺𝟻𝟼𝟽𝟾𝟿-:,"
    
    trans_table = str.maketrans(normal, monospace)
    return text.translate(trans_table)

def get_valid_input(prompt, validation_func):
    """Get valid input from user with validation function"""
    while True:
        try:
            value = input(prompt)
            if validation_func(value):
                return value
            else:
                print(RED_COLOR + "  INVALID INPUT! PLEASE TRY AGAIN.")
        except KeyboardInterrupt:
            print("\nExiting...")
            sys.exit(0)

def validate_pairs(pairs):
    """Validate trading pairs input format"""
    if not pairs:
        return False
    
    # List of valid trading pairs
    VALID_PAIRS = [
        'EURUSD', 'EURUSD-OTC', 'EURUSD_OTC',
        'GBPUSD', 'GBPUSD-OTC', 'GBPUSD_OTC',
        'USDJPY', 'USDJPY-OTC', 'USDJPY_OTC',
        'USDCHF', 'USDCHF-OTC', 'USDCHF_OTC',
        'USDCAD', 'USDCAD-OTC', 'USDCAD_OTC',
        'AUDUSD', 'AUDUSD-OTC', 'AUDUSD_OTC',
        'NZDUSD', 'NZDUSD-OTC', 'NZDUSD_OTC',
        'EURJPY', 'EURJPY-OTC', 'EURJPY_OTC',
        'GBPJPY', 'GBPJPY-OTC', 'GBPJPY_OTC',
        'EURGBP', 'EURGBP-OTC', 'EURGBP_OTC',
        'AUDCAD', 'AUDCAD-OTC', 'AUDCAD_OTC',
        'AUDCHF', 'AUDCHF-OTC', 'AUDCHF_OTC',
        'AUDJPY', 'AUDJPY-OTC', 'AUDJPY_OTC',
        'CHFJPY', 'CHFJPY-OTC', 'CHFJPY_OTC',
        'EURCHF', 'EURCHF-OTC', 'EURCHF_OTC',
        'EURAUD', 'EURAUD-OTC', 'EURAUD_OTC',
        'EURCAD', 'EURCAD-OTC', 'EURCAD_OTC',
        'GBPCHF', 'GBPCHF-OTC', 'GBPCHF_OTC',
        'GBPCAD', 'GBPCAD-OTC', 'GBPCAD_OTC',
        'GBPAUD', 'GBPAUD-OTC', 'GBPAUD_OTC',
        'CADCHF', 'CADCHF-OTC', 'CADCHF_OTC',
        'CADJPY', 'CADJPY-OTC', 'CADJPY_OTC',
        'NZDCAD', 'NZDCAD-OTC', 'NZDCAD_OTC',
        'NZDCHF', 'NZDCHF-OTC', 'NZDCHF_OTC',
        'NZDJPY', 'NZDJPY-OTC', 'NZDJPY_OTC'
    ]
    
    pair_list = [pair.strip().upper() for pair in pairs.split(',') if pair.strip()]
    
    # Check if all pairs are valid
    valid_pairs = [pair for pair in pair_list if pair in VALID_PAIRS]
    
    if len(valid_pairs) == 0:
        print(RED_COLOR + "❌ Invalid pairs! Please use valid trading pairs like:")
        print(WHITE_COLOR + "Examples: EURUSD-OTC, GBPUSD-OTC, USDJPY-OTC, NZDUSD-OTC")
        print(WHITE_COLOR + "Format: EURUSD-OTC,GBPUSD-OTC,USDJPY-OTC")
        return False
    
    return True

def validate_percentage(percentage):
    """Validate percentage input (70-100)"""
    if not percentage.isdigit():
        return False
    
    pct = int(percentage)
    return 70 <= pct <= 100

def validate_day(day):
    """Validate day input (1-10)"""
    if not day.isdigit():
        return False
    
    d = int(day)
    return 1 <= d <= 10

def validate_m1_m2(timeframe):
    """Validate timeframe input (M1, M2, M5)"""
    return timeframe.upper() in ['M1', 'M2', 'M5']

def extract_signals(signals_data):
    """Extract and format trading signals with proper validation"""
    try:
        if not signals_data:
            return []

        formatted_signals = []

        for signal in signals_data:
            try:
                # Handle different signal formats
                if isinstance(signal, dict):
                    # Try multiple field names for pair
                    pair = (signal.get('Pair') or
                           signal.get('symbol') or
                           signal.get('pair') or
                           signal.get('currency_pair') or '')

                    # Try multiple field names for time
                    time_val = (signal.get('Time') or
                               signal.get('time') or
                               signal.get('signal_time') or
                               signal.get('entry_time') or '')

                    # Try multiple field names for direction
                    direction = (signal.get('Direction') or
                                signal.get('direction') or
                                signal.get('signal_direction') or
                                signal.get('type') or '')

                    if not pair or not direction:
                        continue

                    # Normalize direction
                    direction_upper = direction.upper()
                    if 'CALL' in direction_upper or 'BUY' in direction_upper or 'UP' in direction_upper:
                        direction = 'CALL'
                    elif 'PUT' in direction_upper or 'SELL' in direction_upper or 'DOWN' in direction_upper:
                        direction = 'PUT'
                    else:
                        direction = 'CALL'  # Default

                    # Use current time if no time provided
                    if not time_val:
                        time_val = datetime.now().strftime("%H:%M")

                    # Format signal string
                    formatted_signal = f"{time_val} - {pair} - OTC {direction}"

                    # Convert to monospace
                    monospace_signal = to_monospace(formatted_signal)

                    # Add color formatting
                    formatted_signal_with_color = f"{DEAF00_COLOR}{monospace_signal}{RESET_COLOR}"

                    formatted_signals.append(formatted_signal_with_color)

            except Exception:
                continue

        return formatted_signals

    except Exception:
        return []

def main():
    """Main application function"""
    # Show banner first
    show_banner()

    # Perform login authentication with retry
    login_attempts = 0
    max_attempts = 3

    while login_attempts < max_attempts:
        if login():
            break
        else:
            login_attempts += 1
            if login_attempts < max_attempts:
                print(f"{YELLOW_COLOR}Attempt {login_attempts}/{max_attempts} failed. Try again...\n")
                time.sleep(1)
            else:
                print(f"{RED_COLOR}Maximum login attempts exceeded. Exiting...")
                return

    # Welcome message
    print("\n" + "="*50)
    print("🚀 WELCOME TO MT-BUG TRADING SIGNAL GENERATOR")
    print("="*50 + "\n")

    while True:
        try:
            # Display main interface
            print(GREEN_COLOR + BRIGHT_STYLE)

            # Get signal type (OTC or Real)
            get_valid_input(
                "        DO YOU GENERATE OTC OR REAL? (1 for OTC, 2 for Real): ",
                lambda x: x in ['1', '2']
            )

            # Get pairs input with validation
            pairs_input = get_valid_input(
                "  INPUT PAIRS (e.g., EURUSD-OTC,GBPUSD-OTC,USDJPY-OTC): ",
                validate_pairs
            )

            # Parse and clean pairs
            pairs = [pair.strip().upper() for pair in pairs_input.split(',') if pair.strip()]

            # Get percentage with validation
            percentage = get_valid_input(
                "  INPUT MINIMUM PERCENTAGE (70-100): ",
                validate_percentage
            )

            # Get days with validation
            days = get_valid_input(
                "  INPUT DAYS (1-10): ",
                validate_day
            )

            # Get timeframe with validation
            timeframe = get_valid_input(
                "  INPUT TIME_FRAME M1, M2, OR M5: ",
                validate_m1_m2
            ).upper()

            print(RESET_COLOR)

            # Process signals for each pair
            all_signals = []

            for pair in pairs:
                print(f"  FETCHING SIGNALS FOR {pair}")

                # Show loading spinner
                spinner = itertools.cycle(['|', '/', '-', '\\'])

                for _ in range(10):
                    sys.stdout.write(f'\r  Loading... {next(spinner)}')
                    sys.stdout.flush()
                    time.sleep(0.1)

                print()  # New line after spinner

                # Get signals for this pair
                signals = get_signals(pair, timeframe, percentage, days)

                if signals:
                    print(f"  ✅ Found {len(signals)} signals for {pair}")
                    all_signals.extend(signals)
                else:
                    print(f"  ❌ No signals found for {pair}")

            # Sort signals by time if possible
            try:
                sorted_signals = sorted(all_signals, key=lambda x: x.get('Time', ''))
            except:
                sorted_signals = all_signals

            # Extract and display formatted signals
            formatted_signals = extract_signals(sorted_signals)

            if formatted_signals:
                print(f"\n{GREEN_COLOR}{BRIGHT_STYLE}Generated Signals:{RESET_COLOR}")
                print("=" * 50)

                for signal in formatted_signals:
                    print(signal)

                print("=" * 50)
                print(f"{YELLOW_COLOR}1 MINUTE :-{RESET_COLOR}")
            else:
                print(f"{RED_COLOR}  NO VALID SIGNALS FOUND FOR ANY OF THE PAIRS.{RESET_COLOR}")

            # Ask user to continue or quit
            user_input = input(f"\n{MAGENTA_COLOR}        PRESS 'q' TO QUIT OR ENTER TO GENERATE AGAIN: {RESET_COLOR}")

            if user_input.lower() == 'q':
                print(f"{GREEN_COLOR}Thanks For Using MT-BUG Software.. Good Bye...{RESET_COLOR}")
                break
            elif user_input == '':
                print("\n" + "="*50)
                continue
            else:
                print(f"{RED_COLOR}  INVALID INPUT. PLEASE PRESS 'q' TO QUIT OR JUST ENTER TO CONTINUE.{RESET_COLOR}")

        except KeyboardInterrupt:
            print(f"\n{GREEN_COLOR}Thanks For Using MT-BUG Software.. Good Bye...{RESET_COLOR}")
            break
        except Exception as e:
            print(f"{RED_COLOR}An error occurred: {e}{RESET_COLOR}")
            continue

if __name__ == "__main__":
    main()
