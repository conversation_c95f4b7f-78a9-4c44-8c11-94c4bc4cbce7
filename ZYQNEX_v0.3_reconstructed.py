#!/usr/bin/env python3
"""
ZYQNEX v0.3 - Trading Signal Generator
Reconstructed from EXE file
"""

import os
import sys
import time
import json
import requests
import hashlib
import uuid
import getpass
import pytz
import itertools
from datetime import datetime
from colorama import init, Fore, Style

# Initialize colorama
init()

# Color definitions
GREEN_COLOR = Fore.GREEN
RED_COLOR = Fore.RED
YELLOW_COLOR = Fore.YELLOW
MAGENTA_COLOR = Fore.MAGENTA
WHITE_COLOR = Fore.WHITE
BRIGHT_STYLE = Style.BRIGHT
RESET_COLOR = Style.RESET_ALL
DEAF00_COLOR = '\033[38;2;222;175;0m'  # Custom color

def check_license():
    """Check license validity"""
    try:
        # Check for device_uid.txt file
        if not os.path.exists('device_uid.txt'):
            # Generate device UID
            device_uid = str(uuid.uuid4())
            with open('device_uid.txt', 'w') as f:
                f.write(device_uid)
        else:
            with open('device_uid.txt', 'r') as f:
                device_uid = f.read().strip()

        # License check with server
        login_url = "https://admin.tahsinshort.top/login.php"

        # This would typically check license validity
        # For reconstruction purposes, we'll assume valid license
        return True

    except Exception as e:
        print(f"License check error: {e}")
        return False

def get_signals(pair, m1_m2, percentage, days):
    """Fetch signals from API"""
    try:
        start_time_api = time.time()

        # API URL for signal fetching
        api_url = f"https://tahsinshort.top/tahsinboss/apitahsin.php?pair={pair}&percentage={percentage}&advance_filter=on&days={days}"

        print(f"  API RESPONSE RECEIVED FOR {pair}")

        response = requests.get(api_url, timeout=30)
        elapsed_time = time.time() - start_time_api

        print(f"  RESPONSE RECEIVED IN {elapsed_time:.2f} SECONDS")

        if response.status_code == 200:
            try:
                api_data = json.loads(response.text)

                if isinstance(api_data, list):
                    return api_data
                elif isinstance(api_data, dict) and 'signals' in api_data:
                    return api_data['signals']
                else:
                    print("  ERROR: API response is not a list!")
                    return []

            except json.JSONDecodeError:
                print("  ERROR: Invalid JSON response from API!")
                return []
        else:
            print(f"  REQUEST ERROR: HTTP {response.status_code}")
            return []

    except Exception as e:
        print(f"  API ERROR: {e}")
        return []

def fetch_and_parse_signals(pair, days, timeframe, rsi_strategy):
    """Fetch and parse signals from alternative API"""
    try:
        api_url = f"http://tahsinshort.top/api1.php?pair={pair}&days={days}&timeframe={timeframe}&rsi_strategy={rsi_strategy}"

        response = requests.get(api_url, timeout=30)

        if response.status_code == 200:
            try:
                signals_data = json.loads(response.text)

                if not signals_data:
                    return []

                parsed_signals = []
                for item in signals_data:
                    if 'symbol' in item and 'direction' in item:
                        parsed_signals.append(item)

                return parsed_signals

            except json.JSONDecodeError as e:
                print(f"JSON Parsing Error: {e}")
                return []
        else:
            print(f"Failed to fetch data. HTTP Status Code: {response.status_code}")
            return []

    except Exception as e:
        print(f"Exception: {e}")
        return []

def to_monospace(text):
    """Convert text to monospace characters"""
    normal = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-:,"
    monospace = "𝙰𝙱𝙲𝙳𝙴𝙵𝙶𝙷𝙸𝙹𝙺𝙻𝙼𝙽𝙾𝙿𝚀𝚁𝚂𝚃𝚄𝚅𝚆𝚇𝚈𝚉𝚊𝚋𝚌𝚍𝚎𝚏𝚐𝚑𝚒𝚓𝚔𝚕𝚖𝚗𝚘𝚙𝚚𝚛𝚜𝚝𝚞𝚟𝚠𝚡𝚢𝚣𝟶𝟷𝟸𝟹𝟺𝟻𝟼𝟽𝟾𝟿-:,"

    trans_table = str.maketrans(normal, monospace)
    return text.translate(trans_table)

def get_valid_input(prompt, validation_func):
    """Get valid input from user with validation"""
    while True:
        try:
            value = input(prompt)
            if validation_func(value):
                return value
            else:
                print("  INVALID INPUT! PLEASE TRY AGAIN.")
        except KeyboardInterrupt:
            print("\nExiting...")
            sys.exit(0)

def validate_pairs(pairs):
    """Validate trading pairs input"""
    if not pairs:
        return False

    pair_list = [pair.strip() for pair in pairs.split(',') if pair.strip()]

    # Check if pairs end with valid suffixes
    valid_pairs = [pair for pair in pair_list if pair.upper().endswith('-OTC') or not pair.endswith('.org')]

    return len(valid_pairs) > 0

def validate_percentage(percentage):
    """Validate percentage input (70-100)"""
    try:
        pct = int(percentage)
        return 70 <= pct <= 100
    except ValueError:
        return False

def validate_day(day):
    """Validate day input (1-10)"""
    try:
        d = int(day)
        return 1 <= d <= 10
    except ValueError:
        return False

def validate_m1_m2(timeframe):
    """Validate timeframe input"""
    return timeframe.upper() in ['M1', 'M2', 'M5']

def extract_signals(signals_data):
    """Extract and format signals"""
    try:
        if not signals_data:
            print("No signals found.")
            return []

        formatted_signals = []

        for signal in signals_data:
            try:
                # Extract signal information
                pair = signal.get('Pair', signal.get('symbol', ''))
                time_ = signal.get('Time', '')
                time_expiration = signal.get('TimeExpiration', '')
                direction = signal.get('Direction', signal.get('direction', ''))

                if direction.upper() == 'CALL':
                    direction = 'CALL'
                else:
                    direction = 'PUT'

                # Format signal
                if time_expiration:
                    formatted_signal = f"{time_} - {pair} - OTC {direction}"
                else:
                    formatted_signal = f"{time_} - {pair} - OTC {direction}"

                # Convert to monospace
                monospace_signal = to_monospace(formatted_signal)

                # Add color formatting
                formatted_signal_with_color = f"{DEAF00_COLOR}{monospace_signal}{RESET_COLOR}"

                formatted_signals.append(formatted_signal_with_color)

            except Exception as e:
                print(f"  ERROR: Invalid signal format! {e}")
                continue

        return formatted_signals

    except Exception as e:
        print(f"Error extracting signals: {e}")
        return []

def main():
    """Main application function"""
    try:
        # Check license
        if not check_license():
            print("License validation failed!")
            return

        while True:
            try:
                # Get signal type (OTC or Real)
                print(f"{BRIGHT_STYLE}{GREEN_COLOR}")
                signal_type = get_valid_input(
                    "        DO YOU GENERATE OTC OR REAL? (1 for OTC, 2 for Real): ",
                    lambda x: x in ['1', '2']
                )

                # Get pairs input
                pairs_input = get_valid_input(
                    "  INPUT PAIRS (e.g., USDCOP-OTC,USDTRY-OTC): ",
                    validate_pairs
                )

                # Parse pairs
                pairs = [pair.strip().upper() for pair in pairs_input.split(',') if pair.strip()]

                # Get percentage
                percentage = get_valid_input(
                    "  INPUT MINIMUM PERCENTAGE (70-100): ",
                    validate_percentage
                )

                # Get days
                days = get_valid_input(
                    "  INPUT DAYS (1-10): ",
                    validate_day
                )

                # Get timeframe
                timeframe = get_valid_input(
                    "  INPUT TIME_FRAME M1, M2, OR M5: ",
                    validate_m1_m2
                ).upper()

                print(f"{RESET_COLOR}")

                # Fetch signals for each pair
                all_signals = []

                for pair in pairs:
                    print(f"  FETCHING SIGNALS FOR {pair}")

                    # Create spinner effect
                    spinner = itertools.cycle(['|', '/', '-', '\\'])

                    for i in range(10):  # Show spinner for a bit
                        sys.stdout.write(f'\r  Loading... {next(spinner)}')
                        sys.stdout.flush()
                        time.sleep(0.1)

                    print()  # New line after spinner

                    # Fetch signals using the main API
                    signals = get_signals(pair, timeframe, percentage, days)

                    if signals:
                        all_signals.extend(signals)
                    else:
                        # Try alternative API
                        alt_signals = fetch_and_parse_signals(pair, days, timeframe, "true")
                        if alt_signals:
                            all_signals.extend(alt_signals)

                # Sort signals by time
                try:
                    sorted_signals = sorted(all_signals, key=lambda x: x.get('Time', ''))
                except:
                    sorted_signals = all_signals

                # Extract and display signals
                formatted_signals = extract_signals(sorted_signals)

                if formatted_signals:
                    print(f"\n{GREEN_COLOR}{BRIGHT_STYLE}Generated Signals:{RESET_COLOR}")
                    print("=" * 50)

                    for signal in formatted_signals:
                        print(signal)

                    print("=" * 50)
                    print(f"{YELLOW_COLOR}1 MINUTE :-{RESET_COLOR}")
                else:
                    print(f"{RED_COLOR}  NO VALID SIGNALS FOUND FOR ANY OF THE PAIRS.{RESET_COLOR}")

                # Ask user to continue or quit
                user_input = input(f"\n{MAGENTA_COLOR}        PRESS 'q' TO QUIT OR ENTER TO GENERATE AGAIN: {RESET_COLOR}")

                if user_input.lower() == 'q':
                    print(f"{GREEN_COLOR}Thanks For Using QZENEX Software.. Good Bye...{RESET_COLOR}")
                    break
                elif user_input == '':
                    continue
                else:
                    print(f"{RED_COLOR}  INVALID INPUT. PLEASE PRESS 'q' TO QUIT OR JUST ENTER TO CONTINUE.{RESET_COLOR}")

            except KeyboardInterrupt:
                print(f"\n{GREEN_COLOR}Thanks For Using QZENEX Software.. Good Bye...{RESET_COLOR}")
                break
            except Exception as e:
                print(f"{RED_COLOR}An error occurred: {e}{RESET_COLOR}")
                continue

    except Exception as e:
        print(f"Fatal error: {e}")

if __name__ == "__main__":
    main()