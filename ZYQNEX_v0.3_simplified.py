#!/usr/bin/env python3
"""
ZYQNEX v0.3 - Trading Signal Generator (Simplified Version)
Reconstructed from EXE file - Core functionality only
"""

import os
import sys
import time
import json
import requests
import uuid
import itertools
from datetime import datetime

def check_license():
    """Simple license check"""
    try:
        if not os.path.exists('device_uid.txt'):
            device_uid = str(uuid.uuid4())
            with open('device_uid.txt', 'w') as f:
                f.write(device_uid)
        return True
    except:
        return False

def get_signals(pair, timeframe, percentage, days):
    """Fetch signals from API"""
    try:
        start_time = time.time()
        api_url = f"https://tahsinshort.top/tahsinboss/apitahsin.php?pair={pair}&percentage={percentage}&advance_filter=on&days={days}"
        
        print(f"  Fetching signals for {pair}...")
        
        response = requests.get(api_url, timeout=30)
        elapsed_time = time.time() - start_time
        
        print(f"  Response received in {elapsed_time:.2f} seconds")
        
        if response.status_code == 200:
            try:
                data = json.loads(response.text)
                return data if isinstance(data, list) else data.get('signals', [])
            except json.JSONDecodeError:
                print("  Error: Invalid JSON response")
                return []
        else:
            print(f"  Error: HTTP {response.status_code}")
            return []
    except Exception as e:
        print(f"  API Error: {e}")
        return []

def fetch_alternative_signals(pair, days, timeframe, rsi_strategy="true"):
    """Fetch from alternative API"""
    try:
        api_url = f"http://tahsinshort.top/api1.php?pair={pair}&days={days}&timeframe={timeframe}&rsi_strategy={rsi_strategy}"
        response = requests.get(api_url, timeout=30)
        
        if response.status_code == 200:
            try:
                return json.loads(response.text)
            except json.JSONDecodeError:
                return []
        return []
    except:
        return []

def format_signals(signals_data):
    """Format signals for display"""
    if not signals_data:
        return []
    
    formatted = []
    for signal in signals_data:
        try:
            pair = signal.get('Pair', signal.get('symbol', ''))
            time_val = signal.get('Time', '')
            direction = signal.get('Direction', signal.get('direction', ''))
            
            direction = 'CALL' if direction.upper() == 'CALL' else 'PUT'
            formatted_signal = f"{time_val} - {pair} - OTC {direction}"
            formatted.append(formatted_signal)
        except:
            continue
    
    return formatted

def get_valid_input(prompt, validator):
    """Get valid input with validation"""
    while True:
        try:
            value = input(prompt)
            if validator(value):
                return value
            print("  Invalid input! Please try again.")
        except KeyboardInterrupt:
            print("\nExiting...")
            sys.exit(0)

def main():
    """Main application"""
    if not check_license():
        print("License check failed!")
        return
    
    print("ZYQNEX v0.3 - Trading Signal Generator")
    print("=" * 50)
    
    while True:
        try:
            # Get signal type
            signal_type = get_valid_input(
                "Generate OTC or Real? (1 for OTC, 2 for Real): ",
                lambda x: x in ['1', '2']
            )
            
            # Get pairs
            pairs_input = get_valid_input(
                "Input pairs (e.g., USDCOP-OTC,USDTRY-OTC): ",
                lambda x: len(x.strip()) > 0
            )
            pairs = [p.strip().upper() for p in pairs_input.split(',') if p.strip()]
            
            # Get percentage
            percentage = get_valid_input(
                "Input minimum percentage (70-100): ",
                lambda x: x.isdigit() and 70 <= int(x) <= 100
            )
            
            # Get days
            days = get_valid_input(
                "Input days (1-10): ",
                lambda x: x.isdigit() and 1 <= int(x) <= 10
            )
            
            # Get timeframe
            timeframe = get_valid_input(
                "Input timeframe (M1, M2, or M5): ",
                lambda x: x.upper() in ['M1', 'M2', 'M5']
            ).upper()
            
            print("\nFetching signals...")
            print("-" * 30)
            
            all_signals = []
            
            for pair in pairs:
                # Show loading animation
                spinner = itertools.cycle(['|', '/', '-', '\\'])
                for i in range(5):
                    sys.stdout.write(f'\rProcessing {pair}... {next(spinner)}')
                    sys.stdout.flush()
                    time.sleep(0.2)
                print()
                
                # Try main API
                signals = get_signals(pair, timeframe, percentage, days)
                
                if signals:
                    all_signals.extend(signals)
                else:
                    # Try alternative API
                    alt_signals = fetch_alternative_signals(pair, days, timeframe)
                    if alt_signals:
                        all_signals.extend(alt_signals)
            
            # Format and display results
            formatted_signals = format_signals(all_signals)
            
            if formatted_signals:
                print(f"\nGenerated Signals:")
                print("=" * 50)
                for signal in formatted_signals:
                    print(signal)
                print("=" * 50)
                print("Duration: 1 MINUTE")
            else:
                print("No valid signals found for any pairs.")
            
            # Continue or quit
            user_input = input("\nPress 'q' to quit or Enter to generate again: ")
            
            if user_input.lower() == 'q':
                print("Thanks for using ZYQNEX Software. Goodbye!")
                break
            elif user_input == '':
                print("\n" + "="*50)
                continue
            else:
                print("Invalid input. Press 'q' to quit or Enter to continue.")
                
        except KeyboardInterrupt:
            print("\nThanks for using ZYQNEX Software. Goodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")
            continue

if __name__ == "__main__":
    main()
