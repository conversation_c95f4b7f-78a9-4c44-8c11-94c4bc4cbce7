#!/usr/bin/env python3
import re

def extract_strings_from_pyc(filename):
    """Extract readable strings from a .pyc file"""
    with open(filename, 'rb') as f:
        data = f.read()
    
    # Find ASCII strings of length 4 or more
    strings = re.findall(b'[\x20-\x7e]{4,}', data)
    
    print("Extracted strings from the .pyc file:")
    print("=" * 50)
    
    for i, s in enumerate(strings):
        try:
            decoded = s.decode('ascii')
            print(f"{i+1:3d}: {decoded}")
        except:
            pass

if __name__ == "__main__":
    extract_strings_from_pyc("ZYQNEX_v0.3.exe_extracted/ZYQNEX_v0.3.pyc")
