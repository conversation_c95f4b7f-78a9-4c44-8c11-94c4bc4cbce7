#!/usr/bin/env python3
import marshal
import dis
import sys

def extract_pyc(filename):
    """Extract and analyze a .pyc file"""
    try:
        with open(filename, 'rb') as f:
            # Read the magic number (4 bytes)
            magic = f.read(4)
            print(f"Magic bytes: {magic.hex()}")
            
            # Skip the rest of the header (12 bytes for Python 3.10)
            f.read(12)
            
            # Load the code object
            try:
                code = marshal.load(f)
                print(f"Code object loaded successfully")
                print(f"Code name: {code.co_name}")
                print(f"Filename: {code.co_filename}")
                print(f"First line: {code.co_firstlineno}")
                print(f"Argument count: {code.co_argcount}")
                print(f"Local count: {code.co_nlocals}")
                print(f"Stack size: {code.co_stacksize}")
                print(f"Flags: {code.co_flags}")
                print(f"Constants: {code.co_consts[:10]}...")  # First 10 constants
                print(f"Names: {code.co_names[:10]}...")  # First 10 names
                print(f"Variable names: {code.co_varnames[:10]}...")  # First 10 varnames
                
                print("\n" + "="*50)
                print("BYTECODE DISASSEMBLY:")
                print("="*50)
                dis.dis(code)
                
                return code
                
            except Exception as e:
                print(f"Error loading marshal data: {e}")
                return None
                
    except Exception as e:
        print(f"Error reading file: {e}")
        return None

if __name__ == "__main__":
    filename = "ZYQNEX_v0.3.exe_extracted/ZYQNEX_v0.3.pyc"
    code = extract_pyc(filename)
