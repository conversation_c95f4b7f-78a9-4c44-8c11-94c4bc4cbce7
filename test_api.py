#!/usr/bin/env python3
import requests
import json

def test_api():
    """Test the API to see what it returns"""

    # Test different pair formats
    test_pairs = [
        "NZDUSD-OTC",
        "NZDUSD_OTC",
        "NZDUSD",
        "EURUSD-OTC",
        "EURUSD_OTC",
        "EURUSD",
        "USDCAD-OTC",
        "USDCAD"
    ]

    percentage = "80"
    days = "5"

    for pair in test_pairs:
        print(f"\n{'='*60}")
        print(f"Testing pair: {pair}")
        print('='*60)
    
        # Primary API
        url1 = f"https://tahsinshort.top/tahsinboss/apitahsin.php?pair={pair}&percentage={percentage}&advance_filter=on&days={days}&=true"

        print("Primary API:")
        print(f"URL: {url1}")

        try:
            response = requests.get(url1, timeout=10)
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text[:100]}...")

            if response.status_code == 200:
                try:
                    data = json.loads(response.text)
                    if 'error' not in data:
                        print(f"✅ SUCCESS! Found data: {data}")
                        return  # Stop testing if we find working pair
                    else:
                        print(f"❌ Error: {data.get('error')}")
                except json.JSONDecodeError as e:
                    print(f"JSON Error: {e}")
        except Exception as e:
            print(f"Request Error: {e}")

        print("-" * 30)

if __name__ == "__main__":
    test_api()
